import { describe, it, expect } from 'vitest';
import { createGenericBudgetHierarchy } from '$lib/budget_utils';
import type { Tables } from '$lib/database.types';
import type { CostDetailBudgetItem } from '../../routes/org/[org_name]/clients/[client_name]/projects/[project_name]/cost-detail/+page.server';

// Helper function to create a WBS item
function createWbsItem(
	id: string,
	parentId: string | null,
	code: string,
	description: string,
	level: number,
): Tables<'wbs_library_item'> {
	return {
		wbs_library_item_id: id,
		wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
		level,
		in_level_code: code.split('.').pop() || code,
		parent_item_id: parentId,
		code,
		description,
		cost_scope: null,
		item_type: 'Standard',
		client_id: null,
		project_id: null,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z',
	};
}

// Helper function to create a cost detail budget item
function createCostDetailItem(
	wbsItemId: string,
	quantity: number | null = null,
	unitRate: number | null = null,
	factor: number | null = 1,
	budgetAmount: number | null = null,
): CostDetailBudgetItem {
	return {
		wbs_library_item_id: wbsItemId,
		quantity,
		unit_rate: unitRate,
		factor,
		wbs_code: `CODE-${wbsItemId}`,
		wbs_description: `Description for ${wbsItemId}`,
		wbs_level: 1,
		parent_item_id: null,
		budget_amount: budgetAmount,
		work_packages: [],
		purchase_orders: [],
	};
}

describe('Cost Detail Hierarchical Display', () => {
	it('should calculate hierarchical totals correctly', () => {
		const wbsItems = [
			createWbsItem('parent-1', null, '1', 'Parent Item', 1),
			createWbsItem('child-1', 'parent-1', '1.1', 'Child Item 1', 2),
			createWbsItem('child-2', 'parent-1', '1.2', 'Child Item 2', 2),
		];

		const costDetailItems = [
			createCostDetailItem('child-1', 10, 100, 1, 1000), // 10 * 100 * 1 = 1000
			createCostDetailItem('child-2', 5, 200, 1, 1000), // 5 * 200 * 1 = 1000
		];

		const hierarchy = createGenericBudgetHierarchy(wbsItems, costDetailItems);

		// Parent should have hierarchical total of children
		expect(hierarchy.id).toBe('parent-1');
		expect(hierarchy.value).toBe(2000); // 1000 + 1000

		// Children should have their individual values
		expect(hierarchy.children).toHaveLength(2);
		const child1 = hierarchy.children!.find((c) => c.id === 'child-1');
		const child2 = hierarchy.children!.find((c) => c.id === 'child-2');

		expect(child1?.value).toBe(1000);
		expect(child2?.value).toBe(1000);
	});

	it('should handle nested hierarchies with multiple levels', () => {
		const wbsItems = [
			createWbsItem('root', null, '1', 'Root', 1),
			createWbsItem('level2-1', 'root', '1.1', 'Level 2 Item 1', 2),
			createWbsItem('level2-2', 'root', '1.2', 'Level 2 Item 2', 2),
			createWbsItem('level3-1', 'level2-1', '1.1.1', 'Level 3 Item 1', 3),
			createWbsItem('level3-2', 'level2-1', '1.1.2', 'Level 3 Item 2', 3),
		];

		const costDetailItems = [
			createCostDetailItem('level3-1', 1, 100, 1, 100), // 100
			createCostDetailItem('level3-2', 2, 150, 1, 300), // 300
			createCostDetailItem('level2-2', 1, 200, 1, 200), // 200
		];

		const hierarchy = createGenericBudgetHierarchy(wbsItems, costDetailItems);

		// Root should have total of all children
		expect(hierarchy.id).toBe('root');
		expect(hierarchy.value).toBe(600); // 100 + 300 + 200

		// Level 2-1 should have total of its children
		const level2_1 = hierarchy.children!.find((c) => c.id === 'level2-1');
		expect(level2_1?.value).toBe(400); // 100 + 300

		// Level 2-2 should have its own value
		const level2_2 = hierarchy.children!.find((c) => c.id === 'level2-2');
		expect(level2_2?.value).toBe(200);
	});

	it('should handle items without budget data', () => {
		const wbsItems = [
			createWbsItem('parent-1', null, '1', 'Parent Item', 1),
			createWbsItem('child-1', 'parent-1', '1.1', 'Child with data', 2),
			createWbsItem('child-2', 'parent-1', '1.2', 'Child without data', 2),
		];

		const costDetailItems = [
			createCostDetailItem('child-1', 3, 100, 1, 300), // Only child-1 has data
		];

		const hierarchy = createGenericBudgetHierarchy(wbsItems, costDetailItems);

		// Parent should have total from child with data
		expect(hierarchy.id).toBe('parent-1');
		expect(hierarchy.value).toBe(300);

		// Child with data should have its value
		const child1 = hierarchy.children!.find((c) => c.id === 'child-1');
		expect(child1?.value).toBe(300);

		// Child without data should have 0 value
		const child2 = hierarchy.children!.find((c) => c.id === 'child-2');
		expect(child2?.value).toBe(0);
	});
});
